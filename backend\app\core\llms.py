import logging
from app.core.config import settings
from autogen_ext.models.openai import OpenAIChatCompletionClient

logger = logging.getLogger(__name__)

def _setup_model_client():
    """设置DeepSeek模型客户端"""
    if not settings.OPENAI_API_KEY:
        raise ValueError("OPENAI_API_KEY 未设置，请检查环境变量配置")

    model_config = {
        "model": settings.LLM_MODEL,
        "api_key": settings.OPENAI_API_KEY,
        "model_info": {
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": "deepseek",
            "multiple_system_messages": True
        },
    }

    if settings.OPENAI_API_BASE:
        model_config["base_url"] = settings.OPENAI_API_BASE
        logger.info(f"使用自定义API端点: {settings.OPENAI_API_BASE}")

    logger.info(f"初始化模型客户端: {settings.LLM_MODEL}")
    return OpenAIChatCompletionClient(**model_config)

def _setup_vllm_model_client():
    """设置VLLM模型客户端（备用）"""
    if not settings.VLLM_API_KEY:
        logger.warning("VLLM_API_KEY 未设置，VLLM客户端将不可用")
        return None

    model_config = {
        "model": settings.VLLM_API_MODEL,
        "api_key": settings.VLLM_API_KEY,
        "model_info": {
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "family": "qwen",
            "multiple_system_messages": True
        },
    }

    if settings.VLLM_API_URL:
        model_config["base_url"] = settings.VLLM_API_URL
        logger.info(f"初始化VLLM客户端: {settings.VLLM_API_MODEL}")

    return OpenAIChatCompletionClient(**model_config)

# 初始化模型客户端
try:
    model_client = _setup_model_client()
    logger.info("主模型客户端初始化成功")
except Exception as e:
    logger.error(f"主模型客户端初始化失败: {e}")
    raise

try:
    vllm_model_client = _setup_vllm_model_client()
    if vllm_model_client:
        logger.info("VLLM模型客户端初始化成功")
except Exception as e:
    logger.warning(f"VLLM模型客户端初始化失败: {e}")
    vllm_model_client = None
