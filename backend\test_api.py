"""
API测试脚本
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_order_tracking():
    """测试订单跟踪（模拟登录）"""
    print("🧪 测试场景3: 订单追踪")
    
    # 由于没有实现登录接口，我们直接测试数据库查询
    # 这里模拟已登录用户的订单查询
    
    # 测试订单跟踪接口（不需要认证的版本）
    try:
        # 直接查询数据库来验证数据
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))
        
        from app.core.database import SessionLocal
        from app.services.order import OrderService
        
        db = SessionLocal()
        order_service = OrderService(db)
        
        # 测试获取订单跟踪信息
        tracking_info = order_service.get_order_tracking_info("ORD20241201001")
        
        if tracking_info:
            print("✅ 订单跟踪数据查询成功:")
            print(f"   订单号: {tracking_info['order']['order_number']}")
            print(f"   订单状态: {tracking_info['order']['status']}")
            if 'logistics' in tracking_info:
                print(f"   物流公司: {tracking_info['logistics']['logistics_company']}")
                print(f"   运单号: {tracking_info['logistics']['tracking_number']}")
                print(f"   当前状态: {tracking_info['logistics']['current_status']}")
                print(f"   跟踪信息条数: {len(tracking_info['logistics']['tracking_info'])}")
        else:
            print("❌ 订单跟踪数据查询失败")
        
        db.close()
        
    except Exception as e:
        print(f"❌ 订单跟踪测试失败: {e}")

def test_customer_service():
    """测试客服相关功能"""
    print("\n🧪 测试场景4-6: 客服相关功能")
    
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))
        
        from app.core.database import SessionLocal
        from app.services.customer_service import CustomerServiceService
        
        db = SessionLocal()
        cs_service = CustomerServiceService(db)
        
        # 测试退换货资格检查
        eligibility = cs_service.check_refund_eligibility(1)
        print(f"✅ 退换货资格检查: {eligibility}")
        
        # 测试客服统计
        stats = cs_service.get_customer_service_statistics()
        print(f"✅ 客服统计信息: {stats}")
        
        db.close()
        
    except Exception as e:
        print(f"❌ 客服功能测试失败: {e}")

def test_database_data():
    """测试数据库数据完整性"""
    print("\n🧪 测试数据库数据完整性")
    
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))
        
        from app.core.database import SessionLocal
        from app.models.customer_service import Product, Order, Promotion, Coupon
        from app.models.user import User
        
        db = SessionLocal()
        
        # 检查各表数据
        user_count = db.query(User).count()
        product_count = db.query(Product).count()
        order_count = db.query(Order).count()
        promotion_count = db.query(Promotion).count()
        coupon_count = db.query(Coupon).count()
        
        print(f"✅ 数据库数据统计:")
        print(f"   用户数量: {user_count}")
        print(f"   产品数量: {product_count}")
        print(f"   订单数量: {order_count}")
        print(f"   促销活动数量: {promotion_count}")
        print(f"   优惠券数量: {coupon_count}")
        
        # 检查测试订单详情
        test_order = db.query(Order).filter(Order.order_number == "ORD20241201001").first()
        if test_order:
            print(f"✅ 测试订单详情:")
            print(f"   订单号: {test_order.order_number}")
            print(f"   状态: {test_order.status.value}")
            print(f"   金额: {test_order.total_amount}")
            print(f"   商品数量: {len(test_order.order_items)}")
            
            # 检查物流信息
            if test_order.logistics:
                print(f"   物流公司: {test_order.logistics.logistics_company}")
                print(f"   运单号: {test_order.logistics.tracking_number}")
                print(f"   跟踪信息: {len(test_order.logistics.tracking_info or [])}条")
        
        db.close()
        
    except Exception as e:
        print(f"❌ 数据库数据检查失败: {e}")

if __name__ == "__main__":
    print("🚀 YUE智能体平台 - API功能测试")
    print("=" * 50)
    
    test_database_data()
    test_order_tracking()
    test_customer_service()
    
    print("\n🎉 测试完成！")
