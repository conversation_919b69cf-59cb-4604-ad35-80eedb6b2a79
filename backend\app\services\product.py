"""
产品相关业务服务
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.models.customer_service import Product, Category, Inventory
from app.schemas.customer_service import ProductCreate, ProductUpdate, ProductQuery


class ProductService:
    """产品服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_product_by_id(self, product_id: int) -> Optional[Product]:
        """根据ID获取产品"""
        return self.db.query(Product).filter(Product.id == product_id).first()
    
    def get_product_by_sku(self, sku: str) -> Optional[Product]:
        """根据SKU获取产品"""
        return self.db.query(Product).filter(Product.sku == sku).first()
    
    def search_products(self, query: ProductQuery, limit: int = 20, offset: int = 0) -> List[Product]:
        """搜索产品"""
        db_query = self.db.query(Product)
        
        # 构建查询条件
        conditions = []
        
        if query.name:
            conditions.append(Product.name.contains(query.name))
        
        if query.model:
            conditions.append(Product.model.contains(query.model))
        
        if query.sku:
            conditions.append(Product.sku == query.sku)
        
        if query.category_id:
            conditions.append(Product.category_id == query.category_id)
        
        if query.is_active is not None:
            conditions.append(Product.is_active == query.is_active)
        
        if query.is_featured is not None:
            conditions.append(Product.is_featured == query.is_featured)
        
        if query.min_price:
            conditions.append(Product.price >= query.min_price)
        
        if query.max_price:
            conditions.append(Product.price <= query.max_price)
        
        if conditions:
            db_query = db_query.filter(and_(*conditions))
        
        return db_query.offset(offset).limit(limit).all()
    
    def get_product_recommendations(self, product_id: int, limit: int = 3) -> List[Product]:
        """获取产品推荐"""
        # 获取当前产品
        current_product = self.get_product_by_id(product_id)
        if not current_product:
            return []
        
        # 基于分类和价格范围推荐
        from decimal import Decimal
        price_range = current_product.price * Decimal('0.2')  # 20%价格范围
        
        return self.db.query(Product).filter(
            and_(
                Product.id != product_id,
                Product.category_id == current_product.category_id,
                Product.price.between(
                    current_product.price - price_range,
                    current_product.price + price_range
                ),
                Product.is_active == True
            )
        ).limit(limit).all()
    
    def get_product_inventory(self, product_id: int) -> Optional[Inventory]:
        """获取产品库存信息"""
        return self.db.query(Inventory).filter(Inventory.product_id == product_id).first()
    
    def check_product_availability(self, product_id: int, quantity: int = 1) -> bool:
        """检查产品是否有库存"""
        inventory = self.get_product_inventory(product_id)
        if not inventory:
            return False
        return inventory.available_quantity >= quantity
    
    def get_categories(self, parent_id: Optional[int] = None) -> List[Category]:
        """获取分类列表"""
        query = self.db.query(Category).filter(Category.is_active == True)
        if parent_id is not None:
            query = query.filter(Category.parent_id == parent_id)
        return query.all()
    
    def get_featured_products(self, limit: int = 10) -> List[Product]:
        """获取推荐产品"""
        return self.db.query(Product).filter(
            and_(
                Product.is_featured == True,
                Product.is_active == True
            )
        ).limit(limit).all()
    
    def create_product(self, product_data: ProductCreate) -> Product:
        """创建产品"""
        db_product = Product(**product_data.dict())
        self.db.add(db_product)
        self.db.commit()
        self.db.refresh(db_product)
        
        # 创建库存记录
        inventory = Inventory(product_id=db_product.id)
        self.db.add(inventory)
        self.db.commit()
        
        return db_product
    
    def update_product(self, product_id: int, product_data: ProductUpdate) -> Optional[Product]:
        """更新产品"""
        db_product = self.get_product_by_id(product_id)
        if not db_product:
            return None
        
        update_data = product_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_product, field, value)
        
        self.db.commit()
        self.db.refresh(db_product)
        return db_product
    
    def update_inventory(self, product_id: int, stock_quantity: int) -> Optional[Inventory]:
        """更新库存"""
        inventory = self.get_product_inventory(product_id)
        if not inventory:
            return None
        
        inventory.stock_quantity = stock_quantity
        inventory.available_quantity = stock_quantity - inventory.reserved_quantity
        self.db.commit()
        self.db.refresh(inventory)
        return inventory
