# 客服场景API测试指南

本文档提供了6个客服场景的完整API测试示例。

## 环境准备

1. 启动后端服务：
```bash
cd backend
uvicorn app.main:socket_app --reload --host 0.0.0.0 --port 8000
```

2. 初始化数据库：
```bash
cd backend
python scripts/init_db.py
```

3. 访问API文档：http://localhost:8000/docs

## 测试账号

- **管理员**: admin / admin123
- **测试用户**: testuser / test123
- **测试订单号**: ORD20241201001
- **测试优惠券**: WELCOME10, SAVE50

## 场景1: 售前咨询 - 产品信息

### 1.1 搜索产品
```bash
curl -X GET "http://localhost:8000/api/v1/products/search?name=iPhone" \
  -H "accept: application/json"
```

### 1.2 获取产品详情
```bash
curl -X GET "http://localhost:8000/api/v1/products/1" \
  -H "accept: application/json"
```

### 1.3 查询库存状态
```bash
curl -X GET "http://localhost:8000/api/v1/products/1/inventory" \
  -H "accept: application/json"
```

### 1.4 获取产品推荐
```bash
curl -X GET "http://localhost:8000/api/v1/products/1/recommendations?limit=3" \
  -H "accept: application/json"
```

### 1.5 检查库存可用性
```bash
curl -X POST "http://localhost:8000/api/v1/products/1/check-availability?quantity=2" \
  -H "accept: application/json"
```

## 场景2: 售前咨询 - 活动与优惠

### 2.1 获取当前促销活动
```bash
curl -X GET "http://localhost:8000/api/v1/promotions/active" \
  -H "accept: application/json"
```

### 2.2 获取适用的促销活动
```bash
curl -X GET "http://localhost:8000/api/v1/promotions/applicable?order_amount=1500&product_ids=1,2" \
  -H "accept: application/json"
```

### 2.3 获取用户优惠券（需要登录）
```bash
# 首先获取访问令牌
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "test123"}'

# 使用令牌获取优惠券
curl -X GET "http://localhost:8000/api/v1/promotions/coupons/my-coupons" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2.4 验证优惠券
```bash
curl -X POST "http://localhost:8000/api/v1/promotions/coupons/WELCOME10/validate?order_amount=500" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2.5 获取优惠券详情
```bash
curl -X GET "http://localhost:8000/api/v1/promotions/coupons/WELCOME10" \
  -H "accept: application/json"
```

## 场景3: 订单追踪

### 3.1 查询订单跟踪信息
```bash
curl -X GET "http://localhost:8000/api/v1/orders/ORD20241201001/tracking" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3.2 获取用户订单列表
```bash
curl -X GET "http://localhost:8000/api/v1/orders/my-orders?limit=10" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3.3 获取订单详情
```bash
curl -X GET "http://localhost:8000/api/v1/orders/1" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3.4 搜索订单
```bash
curl -X GET "http://localhost:8000/api/v1/orders/search?order_number=ORD20241201001" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 场景4: 售后服务 - 退换货申请

### 4.1 检查退换货资格
```bash
curl -X POST "http://localhost:8000/api/v1/customer-service/refund-requests/1/check-eligibility" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4.2 创建退换货申请
```bash
curl -X POST "http://localhost:8000/api/v1/customer-service/refund-requests" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "order_id": 1,
    "type": "refund",
    "reason": "商品质量问题",
    "description": "收到商品后发现屏幕有划痕",
    "refund_amount": 7999.00,
    "evidence_images": ["evidence1.jpg", "evidence2.jpg"]
  }'
```

### 4.3 获取退换货申请列表
```bash
curl -X GET "http://localhost:8000/api/v1/customer-service/refund-requests" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4.4 获取退换货申请详情
```bash
curl -X GET "http://localhost:8000/api/v1/customer-service/refund-requests/REF20241201001" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 场景5: 投诉与建议

### 5.1 创建投诉
```bash
curl -X POST "http://localhost:8000/api/v1/customer-service/complaints" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "type": "complaint",
    "category": "service",
    "title": "客服态度问题",
    "content": "客服回复不及时，态度不够友好",
    "priority": "normal"
  }'
```

### 5.2 创建建议
```bash
curl -X POST "http://localhost:8000/api/v1/customer-service/complaints" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "type": "suggestion",
    "category": "product",
    "title": "产品改进建议",
    "content": "建议增加更多颜色选择",
    "priority": "low"
  }'
```

### 5.3 获取投诉建议列表
```bash
curl -X GET "http://localhost:8000/api/v1/customer-service/complaints" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 5.4 获取投诉详情
```bash
curl -X GET "http://localhost:8000/api/v1/customer-service/complaints/CMP20241201001" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 场景6: 请求人工服务

### 6.1 创建客服会话
```bash
curl -X POST "http://localhost:8000/api/v1/customer-service/sessions?agent_type=ai" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 6.2 发送消息
```bash
curl -X POST "http://localhost:8000/api/v1/customer-service/sessions/SESSION_ID/messages?content=你好，我需要帮助" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 6.3 转接人工客服
```bash
curl -X POST "http://localhost:8000/api/v1/customer-service/sessions/SESSION_ID/transfer-to-human" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 6.4 获取会话消息
```bash
curl -X GET "http://localhost:8000/api/v1/customer-service/sessions/SESSION_ID/messages" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 6.5 关闭会话
```bash
curl -X POST "http://localhost:8000/api/v1/customer-service/sessions/SESSION_ID/close?satisfaction_rating=5&summary=问题已解决" \
  -H "accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 管理员功能测试

### 更新退换货申请状态
```bash
curl -X PUT "http://localhost:8000/api/v1/customer-service/refund-requests/1/status?status=approved&admin_notes=同意退款" \
  -H "accept: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 更新投诉状态
```bash
curl -X PUT "http://localhost:8000/api/v1/customer-service/complaints/1/status?status=resolved&resolution=问题已处理" \
  -H "accept: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 获取客服统计
```bash
curl -X GET "http://localhost:8000/api/v1/customer-service/statistics" \
  -H "accept: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

## 响应示例

### 产品信息响应
```json
{
  "id": 1,
  "name": "iPhone 15 Pro",
  "model": "A3102",
  "sku": "IPHONE15PRO128",
  "description": "苹果最新旗舰手机",
  "specifications": {
    "颜色": "深空黑色",
    "存储": "128GB",
    "屏幕": "6.1英寸"
  },
  "price": 7999.00,
  "original_price": 8999.00,
  "material": "钛金属",
  "size_guide": "长度: 146.6mm, 宽度: 70.6mm, 厚度: 8.25mm",
  "images": ["iphone15pro_1.jpg", "iphone15pro_2.jpg"],
  "category_id": 1,
  "is_active": true,
  "is_featured": true,
  "created_at": "2024-12-01T10:00:00",
  "updated_at": "2024-12-01T10:00:00"
}
```

### 订单跟踪响应
```json
{
  "order": {
    "order_number": "ORD20241201001",
    "status": "delivered",
    "created_at": "2024-12-01T10:00:00",
    "total_amount": 7999.00
  },
  "logistics": {
    "tracking_number": "SF1234567890",
    "logistics_company": "顺丰速运",
    "current_status": "已签收",
    "estimated_delivery": null,
    "actual_delivery": "2024-12-02T15:30:00",
    "tracking_info": [
      {
        "time": "2024-12-01 10:00:00",
        "status": "已下单",
        "location": "北京"
      },
      {
        "time": "2024-12-02 15:30:00",
        "status": "已签收",
        "location": "朝阳区测试街道"
      }
    ],
    "last_updated": "2024-12-02T15:30:00"
  }
}
```

## 错误处理

所有API都会返回标准的HTTP状态码和错误信息：

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

错误响应格式：
```json
{
  "error": "RESOURCE_NOT_FOUND",
  "message": "产品不存在",
  "detail": "Product with id 999 not found"
}
```
