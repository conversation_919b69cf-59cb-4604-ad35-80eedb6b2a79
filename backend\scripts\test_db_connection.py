"""
测试数据库连接脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

import pymysql
from app.core.config import settings

def test_mysql_connection():
    """测试MySQL连接"""
    print("🔍 测试MySQL数据库连接...")
    print(f"📍 服务器: {settings.MYSQL_SERVER}:{settings.MYSQL_PORT}")
    print(f"👤 用户: {settings.MYSQL_USER}")
    print(f"🗄️ 数据库: {settings.MYSQL_DB}")
    
    try:
        # 测试连接到MySQL服务器
        connection = pymysql.connect(
            host=settings.MYSQL_SERVER,
            port=settings.MYSQL_PORT,
            user=settings.MYSQL_USER,
            password=settings.MYSQL_PASSWORD,
            charset='utf8mb4'
        )
        
        print("✅ MySQL服务器连接成功!")
        
        # 检查数据库是否存在
        with connection.cursor() as cursor:
            cursor.execute("SHOW DATABASES LIKE %s", (settings.MYSQL_DB,))
            result = cursor.fetchone()
            
            if result:
                print(f"✅ 数据库 '{settings.MYSQL_DB}' 已存在")
            else:
                print(f"⚠️ 数据库 '{settings.MYSQL_DB}' 不存在，正在创建...")
                cursor.execute(f"CREATE DATABASE {settings.MYSQL_DB} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                print(f"✅ 数据库 '{settings.MYSQL_DB}' 创建成功!")
        
        connection.close()
        
        # 测试连接到指定数据库
        connection = pymysql.connect(
            host=settings.MYSQL_SERVER,
            port=settings.MYSQL_PORT,
            user=settings.MYSQL_USER,
            password=settings.MYSQL_PASSWORD,
            database=settings.MYSQL_DB,
            charset='utf8mb4'
        )
        
        print(f"✅ 数据库 '{settings.MYSQL_DB}' 连接成功!")
        
        # 显示数据库信息
        with connection.cursor() as cursor:
            cursor.execute("SELECT DATABASE(), VERSION(), @@character_set_database, @@collation_database")
            db_info = cursor.fetchone()
            print(f"📊 当前数据库: {db_info[0]}")
            print(f"🔢 MySQL版本: {db_info[1]}")
            print(f"🔤 字符集: {db_info[2]}")
            print(f"📝 排序规则: {db_info[3]}")
        
        connection.close()
        return True
        
    except pymysql.Error as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def test_sqlalchemy_connection():
    """测试SQLAlchemy连接"""
    print("\n🔍 测试SQLAlchemy连接...")
    
    try:
        from app.core.database import SessionLocal, engine
        
        # 测试引擎连接
        with engine.connect() as conn:
            result = conn.execute("SELECT 1 as test")
            print("✅ SQLAlchemy引擎连接成功!")
        
        # 测试会话
        db = SessionLocal()
        try:
            result = db.execute("SELECT DATABASE() as current_db, VERSION() as version")
            row = result.fetchone()
            print(f"✅ SQLAlchemy会话连接成功!")
            print(f"📊 当前数据库: {row[0]}")
            print(f"🔢 MySQL版本: {row[1]}")
        finally:
            db.close()
        
        return True
        
    except Exception as e:
        print(f"❌ SQLAlchemy连接失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 YUE智能体平台 - 数据库连接测试")
    print("=" * 50)
    
    # 测试基础MySQL连接
    mysql_ok = test_mysql_connection()
    
    if mysql_ok:
        # 测试SQLAlchemy连接
        sqlalchemy_ok = test_sqlalchemy_connection()
        
        if sqlalchemy_ok:
            print("\n🎉 所有数据库连接测试通过!")
            print("\n📋 下一步:")
            print("1. 运行数据库迁移: alembic upgrade head")
            print("2. 初始化测试数据: python scripts/init_db.py")
            print("3. 启动服务: python start_server.py")
        else:
            print("\n❌ SQLAlchemy连接测试失败，请检查配置")
    else:
        print("\n❌ MySQL连接测试失败，请检查:")
        print("1. MySQL服务是否启动")
        print("2. 用户名密码是否正确")
        print("3. 端口是否正确")
        print("4. 是否安装了pymysql: pip install pymysql")
