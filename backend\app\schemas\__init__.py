"""
Pydantic模型包
"""

from .customer_service import *
from .user import *

__all__ = [
    # 用户相关
    "UserBase", "UserCreate", "UserUpdate", "UserInDB", "User",
    
    # 产品相关
    "ProductBase", "ProductCreate", "ProductUpdate", "Product",
    "CategoryBase", "CategoryCreate", "Category",
    "InventoryBase", "Inventory",
    
    # 订单相关
    "OrderBase", "OrderCreate", "OrderUpdate", "Order",
    "OrderItemBase", "OrderItem",
    "LogisticsTrackingBase", "LogisticsTracking",
    
    # 促销相关
    "PromotionBase", "Promotion",
    "CouponBase", "Coupon",
    
    # 售后相关
    "RefundRequestBase", "RefundRequestCreate", "RefundRequest",
    "ComplaintBase", "ComplaintCreate", "Complaint",
    
    # 客服相关
    "CustomerServiceSessionBase", "CustomerServiceSession",
    "CustomerServiceMessageBase", "CustomerServiceMessage",
]
