#!/usr/bin/env python3
"""
大模型集成测试脚本
测试DeepSeek模型配置和流式输出功能
"""

import asyncio
import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_model_client_config():
    """测试模型客户端配置"""
    print("🔧 测试模型客户端配置")
    print("-" * 50)
    
    try:
        from app.core.config import settings
        from app.core.llms import model_client
        
        print(f"✅ 配置信息:")
        print(f"   - API Key: {settings.OPENAI_API_KEY[:10]}...{settings.OPENAI_API_KEY[-4:] if settings.OPENAI_API_KEY else 'None'}")
        print(f"   - API Base: {settings.OPENAI_API_BASE}")
        print(f"   - Model: {settings.LLM_MODEL}")
        
        print(f"✅ 模型客户端: {type(model_client).__name__}")
        print(f"   - 模型配置: {getattr(model_client, '_model', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型客户端配置失败: {e}")
        return False

async def test_basic_chat():
    """测试基础对话功能"""
    print("\n💬 测试基础对话功能")
    print("-" * 50)
    
    try:
        from app.agents.customer_service.chat_service import ChatService
        from app.schemas.customer import ChatMessage
        
        # 创建聊天服务
        chat_service = ChatService()
        
        # 创建测试消息
        test_messages = [
            ChatMessage(role="user", content="你好，请介绍一下你自己")
        ]
        
        print("📤 发送消息: 你好，请介绍一下你自己")
        print("📥 AI回复:")
        
        # 测试流式响应
        response_chunks = []
        async for chunk in chat_service.chat_stream(
            messages=test_messages,
            user_id="test_user"
        ):
            print(chunk, end="", flush=True)
            response_chunks.append(chunk)
        
        print("\n")
        
        if response_chunks:
            print("✅ 流式对话测试成功")
            print(f"   - 响应片段数: {len(response_chunks)}")
            print(f"   - 总响应长度: {len(''.join(response_chunks))} 字符")
            return True
        else:
            print("❌ 未收到任何响应")
            return False
            
    except Exception as e:
        print(f"❌ 基础对话测试失败: {e}")
        logger.exception("详细错误信息:")
        return False

async def test_tool_calling():
    """测试工具调用功能"""
    print("\n🔧 测试工具调用功能")
    print("-" * 50)
    
    try:
        from app.agents.customer_service.chat_service import ChatService
        from app.schemas.customer import ChatMessage
        
        # 创建聊天服务
        chat_service = ChatService()
        
        # 创建测试消息 - 询问产品信息
        test_messages = [
            ChatMessage(role="user", content="请帮我查询产品ID为1的商品详情")
        ]
        
        print("📤 发送消息: 请帮我查询产品ID为1的商品详情")
        print("📥 AI回复:")
        
        # 测试工具调用
        response_chunks = []
        async for chunk in chat_service.chat_stream(
            messages=test_messages,
            user_id="test_user"
        ):
            print(chunk, end="", flush=True)
            response_chunks.append(chunk)
        
        print("\n")
        
        full_response = ''.join(response_chunks)
        if "产品" in full_response or "商品" in full_response:
            print("✅ 工具调用测试成功")
            return True
        else:
            print("⚠️ 工具调用可能未正常工作")
            return False
            
    except Exception as e:
        print(f"❌ 工具调用测试失败: {e}")
        logger.exception("详细错误信息:")
        return False

async def test_memory_persistence():
    """测试记忆持久化功能"""
    print("\n🧠 测试记忆持久化功能")
    print("-" * 50)
    
    try:
        from app.agents.customer_service.chat_service import ChatService
        from app.schemas.customer import ChatMessage
        
        # 创建聊天服务
        chat_service = ChatService()
        user_id = "memory_test_user"
        
        # 第一轮对话
        print("📤 第一轮对话: 我的名字是张三")
        messages1 = [ChatMessage(role="user", content="我的名字是张三，请记住")]
        
        async for chunk in chat_service.chat_stream(
            messages=messages1,
            user_id=user_id
        ):
            print(chunk, end="", flush=True)
        print("\n")
        
        # 等待一下确保记忆保存
        await asyncio.sleep(1)
        
        # 第二轮对话 - 测试记忆
        print("📤 第二轮对话: 你还记得我的名字吗？")
        messages2 = [ChatMessage(role="user", content="你还记得我的名字吗？")]
        
        response_chunks = []
        async for chunk in chat_service.chat_stream(
            messages=messages2,
            user_id=user_id
        ):
            print(chunk, end="", flush=True)
            response_chunks.append(chunk)
        print("\n")
        
        full_response = ''.join(response_chunks)
        if "张三" in full_response:
            print("✅ 记忆持久化测试成功")
            return True
        else:
            print("⚠️ 记忆持久化可能未正常工作")
            return False
            
    except Exception as e:
        print(f"❌ 记忆持久化测试失败: {e}")
        logger.exception("详细错误信息:")
        return False

async def test_streaming_performance():
    """测试流式输出性能"""
    print("\n⚡ 测试流式输出性能")
    print("-" * 50)
    
    try:
        import time
        from app.agents.customer_service.chat_service import ChatService
        from app.schemas.customer import ChatMessage
        
        # 创建聊天服务
        chat_service = ChatService()
        
        # 创建测试消息
        test_messages = [
            ChatMessage(role="user", content="请详细介绍一下电商平台的主要功能和特色")
        ]
        
        print("📤 发送消息: 请详细介绍一下电商平台的主要功能和特色")
        print("📊 性能指标:")
        
        start_time = time.time()
        first_chunk_time = None
        chunk_count = 0
        total_chars = 0
        
        async for chunk in chat_service.chat_stream(
            messages=test_messages,
            user_id="perf_test_user"
        ):
            if first_chunk_time is None:
                first_chunk_time = time.time()
                print(f"   - 首个响应时间: {first_chunk_time - start_time:.2f}秒")
            
            chunk_count += 1
            total_chars += len(chunk)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"   - 总响应时间: {total_time:.2f}秒")
        print(f"   - 响应片段数: {chunk_count}")
        print(f"   - 总字符数: {total_chars}")
        print(f"   - 平均速度: {total_chars/total_time:.1f} 字符/秒")
        
        if first_chunk_time and (first_chunk_time - start_time) < 5.0:
            print("✅ 流式输出性能测试通过")
            return True
        else:
            print("⚠️ 流式输出响应较慢")
            return False
            
    except Exception as e:
        print(f"❌ 流式输出性能测试失败: {e}")
        logger.exception("详细错误信息:")
        return False

async def main():
    """主测试函数"""
    print("🚀 DeepSeek大模型集成测试")
    print("=" * 60)
    
    # 测试结果统计
    test_results = []
    
    # 1. 测试模型客户端配置
    result1 = await test_model_client_config()
    test_results.append(("模型客户端配置", result1))
    
    if not result1:
        print("\n❌ 模型配置失败，无法继续后续测试")
        return
    
    # 2. 测试基础对话功能
    result2 = await test_basic_chat()
    test_results.append(("基础对话功能", result2))
    
    # 3. 测试工具调用功能
    result3 = await test_tool_calling()
    test_results.append(("工具调用功能", result3))
    
    # 4. 测试记忆持久化功能
    result4 = await test_memory_persistence()
    test_results.append(("记忆持久化功能", result4))
    
    # 5. 测试流式输出性能
    result5 = await test_streaming_performance()
    test_results.append(("流式输出性能", result5))
    
    # 输出测试总结
    print("\n📊 测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！大模型集成成功！")
    elif passed >= total * 0.8:
        print("⚠️ 大部分测试通过，但仍有问题需要解决")
    else:
        print("❌ 多项测试失败，需要检查配置和代码")

if __name__ == "__main__":
    # 确保在正确的目录中运行
    os.chdir(Path(__file__).parent)
    
    # 运行测试
    asyncio.run(main())
