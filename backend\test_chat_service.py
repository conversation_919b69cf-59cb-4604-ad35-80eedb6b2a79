#!/usr/bin/env python3
"""
ChatService流式输出测试脚本
专门测试智能客服的流式对话功能
"""

import asyncio
import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_chat_service_basic():
    """测试ChatService基础功能"""
    print("💬 测试ChatService基础功能")
    print("-" * 50)
    
    try:
        from app.agents.customer_service.chat_service import ChatService
        from app.schemas.customer import ChatMessage
        
        # 创建聊天服务
        chat_service = ChatService()
        print("✅ ChatService初始化成功")
        
        # 创建测试消息
        test_messages = [
            ChatMessage(role="user", content="你好")
        ]
        
        print("📤 发送测试消息: 你好")
        print("📥 AI回复:")
        
        # 测试流式响应
        response_chunks = []
        try:
            async for chunk in chat_service.chat_stream(
                messages=test_messages,
                user_id="test_user"
            ):
                print(chunk, end="", flush=True)
                response_chunks.append(chunk)
        except Exception as e:
            print(f"\n❌ 流式对话过程中出错: {e}")
            logger.exception("详细错误信息:")
            return False
        
        print("\n")
        
        if response_chunks:
            print("✅ 流式对话测试成功")
            print(f"   - 响应片段数: {len(response_chunks)}")
            print(f"   - 总响应长度: {len(''.join(response_chunks))} 字符")
            return True
        else:
            print("❌ 未收到任何响应")
            return False
            
    except Exception as e:
        print(f"❌ ChatService测试失败: {e}")
        logger.exception("详细错误信息:")
        return False

async def test_chat_service_with_tools():
    """测试ChatService工具调用功能"""
    print("\n🔧 测试ChatService工具调用功能")
    print("-" * 50)
    
    try:
        from app.agents.customer_service.chat_service import ChatService
        from app.schemas.customer import ChatMessage
        
        # 创建聊天服务
        chat_service = ChatService()
        
        # 创建测试消息 - 询问产品信息（会触发工具调用）
        test_messages = [
            ChatMessage(role="user", content="请帮我查询产品ID为1的商品信息")
        ]
        
        print("📤 发送消息: 请帮我查询产品ID为1的商品信息")
        print("📥 AI回复:")
        
        # 测试工具调用
        response_chunks = []
        try:
            async for chunk in chat_service.chat_stream(
                messages=test_messages,
                user_id="test_user_tools"
            ):
                print(chunk, end="", flush=True)
                response_chunks.append(chunk)
        except Exception as e:
            print(f"\n❌ 工具调用过程中出错: {e}")
            logger.exception("详细错误信息:")
            return False
        
        print("\n")
        
        full_response = ''.join(response_chunks)
        if response_chunks and ("产品" in full_response or "商品" in full_response or "工具调用" in full_response):
            print("✅ 工具调用测试成功")
            return True
        else:
            print("⚠️ 工具调用可能未正常工作，但基础对话正常")
            return len(response_chunks) > 0
            
    except Exception as e:
        print(f"❌ 工具调用测试失败: {e}")
        logger.exception("详细错误信息:")
        return False

async def test_chat_service_memory():
    """测试ChatService记忆功能"""
    print("\n🧠 测试ChatService记忆功能")
    print("-" * 50)
    
    try:
        from app.agents.customer_service.chat_service import ChatService
        from app.schemas.customer import ChatMessage
        
        # 创建聊天服务
        chat_service = ChatService()
        user_id = "memory_test_user"
        
        # 第一轮对话
        print("📤 第一轮对话: 我的名字是张三")
        messages1 = [ChatMessage(role="user", content="我的名字是张三，请记住")]
        
        response1_chunks = []
        try:
            async for chunk in chat_service.chat_stream(
                messages=messages1,
                user_id=user_id
            ):
                print(chunk, end="", flush=True)
                response1_chunks.append(chunk)
        except Exception as e:
            print(f"\n❌ 第一轮对话失败: {e}")
            return False
        
        print("\n")
        
        # 等待一下确保记忆保存
        await asyncio.sleep(1)
        
        # 第二轮对话 - 测试记忆
        print("📤 第二轮对话: 你还记得我的名字吗？")
        messages2 = [ChatMessage(role="user", content="你还记得我的名字吗？")]
        
        response2_chunks = []
        try:
            async for chunk in chat_service.chat_stream(
                messages=messages2,
                user_id=user_id
            ):
                print(chunk, end="", flush=True)
                response2_chunks.append(chunk)
        except Exception as e:
            print(f"\n❌ 第二轮对话失败: {e}")
            return False
        
        print("\n")
        
        full_response2 = ''.join(response2_chunks)
        if "张三" in full_response2:
            print("✅ 记忆功能测试成功")
            return True
        else:
            print("⚠️ 记忆功能可能未正常工作，但对话正常")
            return len(response2_chunks) > 0
            
    except Exception as e:
        print(f"❌ 记忆功能测试失败: {e}")
        logger.exception("详细错误信息:")
        return False

async def test_streaming_performance():
    """测试流式输出性能"""
    print("\n⚡ 测试流式输出性能")
    print("-" * 50)
    
    try:
        import time
        from app.agents.customer_service.chat_service import ChatService
        from app.schemas.customer import ChatMessage
        
        # 创建聊天服务
        chat_service = ChatService()
        
        # 创建测试消息
        test_messages = [
            ChatMessage(role="user", content="请详细介绍一下你的功能")
        ]
        
        print("📤 发送消息: 请详细介绍一下你的功能")
        print("📊 性能指标:")
        
        start_time = time.time()
        first_chunk_time = None
        chunk_count = 0
        total_chars = 0
        
        try:
            async for chunk in chat_service.chat_stream(
                messages=test_messages,
                user_id="perf_test_user"
            ):
                if first_chunk_time is None:
                    first_chunk_time = time.time()
                    print(f"   - 首个响应时间: {first_chunk_time - start_time:.2f}秒")
                
                chunk_count += 1
                total_chars += len(chunk)
        except Exception as e:
            print(f"\n❌ 性能测试过程中出错: {e}")
            return False
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"   - 总响应时间: {total_time:.2f}秒")
        print(f"   - 响应片段数: {chunk_count}")
        print(f"   - 总字符数: {total_chars}")
        if total_time > 0:
            print(f"   - 平均速度: {total_chars/total_time:.1f} 字符/秒")
        
        if first_chunk_time and (first_chunk_time - start_time) < 10.0 and chunk_count > 0:
            print("✅ 流式输出性能测试通过")
            return True
        else:
            print("⚠️ 流式输出响应较慢或无响应")
            return False
            
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        logger.exception("详细错误信息:")
        return False

async def main():
    """主测试函数"""
    print("🚀 ChatService流式输出测试")
    print("=" * 60)
    
    # 测试结果统计
    test_results = []
    
    # 1. 测试基础功能
    result1 = await test_chat_service_basic()
    test_results.append(("基础对话功能", result1))
    
    if not result1:
        print("\n❌ 基础功能失败，无法继续后续测试")
        return
    
    # 2. 测试工具调用功能
    result2 = await test_chat_service_with_tools()
    test_results.append(("工具调用功能", result2))
    
    # 3. 测试记忆功能
    result3 = await test_chat_service_memory()
    test_results.append(("记忆功能", result3))
    
    # 4. 测试流式输出性能
    result4 = await test_streaming_performance()
    test_results.append(("流式输出性能", result4))
    
    # 输出测试总结
    print("\n📊 测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！ChatService流式输出功能正常！")
    elif passed >= total * 0.75:
        print("⚠️ 大部分测试通过，基本功能正常")
    else:
        print("❌ 多项测试失败，需要检查配置和代码")

if __name__ == "__main__":
    # 确保在正确的目录中运行
    os.chdir(Path(__file__).parent)
    
    # 运行测试
    asyncio.run(main())
