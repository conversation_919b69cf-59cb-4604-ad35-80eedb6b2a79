<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="@localhost" uuid="16754795-fc71-4a06-9eb2-403c3a88b724">
      <driver-ref>mysql_aurora.aws_wrapper</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>software.amazon.jdbc.Driver</jdbc-driver>
      <jdbc-url>jdbc:aws-wrapper:mysql://localhost:3306</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>