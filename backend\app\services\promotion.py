"""
促销活动相关业务服务
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from datetime import datetime
from decimal import Decimal

from app.models.customer_service import Promotion, Coupon, UserCoupon, PromotionType


class PromotionService:
    """促销活动服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_active_promotions(self) -> List[Promotion]:
        """获取当前有效的促销活动"""
        now = datetime.now()
        return self.db.query(Promotion).filter(
            and_(
                Promotion.is_active == True,
                Promotion.start_time <= now,
                Promotion.end_time >= now
            )
        ).order_by(Promotion.priority.desc()).all()
    
    def get_promotion_by_id(self, promotion_id: int) -> Optional[Promotion]:
        """根据ID获取促销活动"""
        return self.db.query(Promotion).filter(Promotion.id == promotion_id).first()
    
    def get_coupon_by_code(self, code: str) -> Optional[Coupon]:
        """根据优惠券代码获取优惠券"""
        return self.db.query(Coupon).filter(Coupon.code == code).first()
    
    def get_user_coupons(self, user_id: int, is_used: Optional[bool] = None) -> List[UserCoupon]:
        """获取用户优惠券"""
        query = self.db.query(UserCoupon).filter(UserCoupon.user_id == user_id)
        
        if is_used is not None:
            query = query.filter(UserCoupon.is_used == is_used)
        
        return query.all()
    
    def get_available_coupons(self, user_id: int) -> List[Coupon]:
        """获取用户可用的优惠券"""
        now = datetime.now()
        
        # 获取用户已领取且未使用的优惠券
        user_coupon_ids = self.db.query(UserCoupon.coupon_id).filter(
            and_(
                UserCoupon.user_id == user_id,
                UserCoupon.is_used == False
            )
        ).subquery()
        
        return self.db.query(Coupon).filter(
            and_(
                Coupon.id.in_(user_coupon_ids),
                Coupon.is_active == True,
                Coupon.start_time <= now,
                Coupon.end_time >= now
            )
        ).all()
    
    def validate_coupon(self, code: str, user_id: int, order_amount: Decimal) -> Dict[str, Any]:
        """验证优惠券是否可用"""
        coupon = self.get_coupon_by_code(code)
        if not coupon:
            return {"valid": False, "message": "优惠券不存在"}
        
        # 检查优惠券是否有效
        now = datetime.now()
        if not coupon.is_active:
            return {"valid": False, "message": "优惠券已失效"}
        
        if coupon.start_time > now:
            return {"valid": False, "message": "优惠券尚未生效"}
        
        if coupon.end_time < now:
            return {"valid": False, "message": "优惠券已过期"}
        
        # 检查使用次数限制
        if coupon.usage_limit and coupon.used_count >= coupon.usage_limit:
            return {"valid": False, "message": "优惠券使用次数已达上限"}
        
        # 检查用户是否拥有此优惠券
        user_coupon = self.db.query(UserCoupon).filter(
            and_(
                UserCoupon.user_id == user_id,
                UserCoupon.coupon_id == coupon.id,
                UserCoupon.is_used == False
            )
        ).first()
        
        if not user_coupon:
            return {"valid": False, "message": "您没有此优惠券或已使用"}
        
        # 检查最低订单金额
        if coupon.min_order_amount and order_amount < coupon.min_order_amount:
            return {
                "valid": False, 
                "message": f"订单金额需满{coupon.min_order_amount}元才能使用此优惠券"
            }
        
        # 计算优惠金额
        discount_amount = self.calculate_discount(coupon, order_amount)
        
        return {
            "valid": True,
            "coupon": coupon,
            "discount_amount": discount_amount,
            "message": "优惠券可用"
        }
    
    def calculate_discount(self, coupon: Coupon, order_amount: Decimal) -> Decimal:
        """计算优惠金额"""
        if coupon.discount_type == "percentage":
            discount = order_amount * (coupon.discount_value / 100)
        else:  # fixed_amount
            discount = coupon.discount_value
        
        # 应用最大优惠金额限制
        if coupon.max_discount_amount:
            discount = min(discount, coupon.max_discount_amount)
        
        # 优惠金额不能超过订单金额
        discount = min(discount, order_amount)
        
        return discount
    
    def use_coupon(self, code: str, user_id: int, order_id: int) -> bool:
        """使用优惠券"""
        user_coupon = self.db.query(UserCoupon).join(Coupon).filter(
            and_(
                UserCoupon.user_id == user_id,
                Coupon.code == code,
                UserCoupon.is_used == False
            )
        ).first()
        
        if not user_coupon:
            return False
        
        # 标记为已使用
        user_coupon.is_used = True
        user_coupon.used_at = datetime.now()
        user_coupon.order_id = order_id
        
        # 增加优惠券使用次数
        coupon = user_coupon.coupon
        coupon.used_count += 1
        
        self.db.commit()
        return True
    
    def give_coupon_to_user(self, user_id: int, coupon_id: int) -> UserCoupon:
        """给用户发放优惠券"""
        user_coupon = UserCoupon(
            user_id=user_id,
            coupon_id=coupon_id
        )
        self.db.add(user_coupon)
        self.db.commit()
        self.db.refresh(user_coupon)
        return user_coupon
    
    def get_applicable_promotions(self, order_amount: Decimal, product_ids: List[int] = None) -> List[Dict[str, Any]]:
        """获取适用的促销活动"""
        active_promotions = self.get_active_promotions()
        applicable = []
        
        for promotion in active_promotions:
            rules = promotion.rules or {}
            
            # 检查订单金额条件
            min_amount = rules.get("min_order_amount")
            if min_amount and order_amount < Decimal(str(min_amount)):
                continue
            
            # 检查商品条件（如果有指定商品）
            applicable_products = rules.get("applicable_products")
            if applicable_products and product_ids:
                if not any(pid in applicable_products for pid in product_ids):
                    continue
            
            # 计算优惠信息
            discount_info = self.calculate_promotion_discount(promotion, order_amount)
            
            applicable.append({
                "promotion": promotion,
                "discount_amount": discount_info["discount_amount"],
                "description": discount_info["description"]
            })
        
        return applicable
    
    def calculate_promotion_discount(self, promotion: Promotion, order_amount: Decimal) -> Dict[str, Any]:
        """计算促销活动优惠"""
        rules = promotion.rules or {}
        discount_amount = Decimal("0")
        description = ""
        
        if promotion.type == PromotionType.DISCOUNT:
            # 折扣
            discount_rate = Decimal(str(rules.get("discount_rate", 0))) / 100
            discount_amount = order_amount * discount_rate
            description = f"{rules.get('discount_rate', 0)}%折扣"
        
        elif promotion.type == PromotionType.FULL_REDUCTION:
            # 满减
            thresholds = rules.get("thresholds", [])
            for threshold in sorted(thresholds, key=lambda x: x["min_amount"], reverse=True):
                if order_amount >= Decimal(str(threshold["min_amount"])):
                    discount_amount = Decimal(str(threshold["discount_amount"]))
                    description = f"满{threshold['min_amount']}减{threshold['discount_amount']}"
                    break
        
        elif promotion.type == PromotionType.FREE_SHIPPING:
            # 包邮（这里假设运费在其他地方处理）
            description = "免运费"
        
        return {
            "discount_amount": discount_amount,
            "description": description
        }
