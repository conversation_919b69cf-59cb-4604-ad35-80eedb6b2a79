# FastAPI 后端的基础 URL
import json
import os
from typing import Union, Dict, Optional, List

import requests

BASE_API_URL = os.getenv("FASTAPI_BASE_URL", "http://127.0.0.1:8001")

# --- 工具函数 (调用 FastAPI) ---
# (工具函数 _call_api, get_product_details, search_products, get_order_status,
#  get_active_promotions, get_policy, check_return_eligibility,
#  submit_return_request, log_feedback 保持不变 - 此处省略)
def _call_api(method: str, endpoint: str, params: Optional[Dict] = None, json_data: Optional[Dict] = None) -> Union[Dict, str]:
    """通用 API 调用函数"""
    url = f"{BASE_API_URL}{endpoint}"
    try:
        response = requests.request(method, url, params=params, json=json_data, timeout=15)
        response.raise_for_status() # 如果状态码不是 2xx，则引发 HTTPError
        try:
            return response.json()
        except json.JSONDecodeError:
            if not response.text:
                 return {"status": "success", "message": "操作成功完成，无内容返回。"}
            return response.text
    except requests.exceptions.RequestException as e:
        error_message = f"API 调用失败: {url} - {str(e)}"
        try:
            error_detail = e.response.json() if e.response else str(e)
            error_message += f" | 详情: {error_detail}"
        except:
             error_message += f" | 无法解析错误详情。"
        print(error_message)
        return error_message
    except Exception as e:
        error_message = f"处理 API 响应时发生未知错误: {url} - {str(e)}"
        print(error_message)
        return error_message


# 工具函数定义（调用电商API）

def get_product_details(product_id: int) -> Union[Dict, str]:
    """根据商品 ID 获取商品的详细信息。"""
    print(f"工具调用: get_product_details(product_id={product_id})")
    if not isinstance(product_id, int) or product_id <= 0:
        return "错误：product_id 必须是一个正整数。"
    return _call_api("GET", f"/api/v1/products/{product_id}")

def search_products(name: Optional[str] = None, category: Optional[str] = None,
                   min_price: Optional[float] = None, max_price: Optional[float] = None) -> Union[Dict, str]:
    """搜索产品"""
    print(f"工具调用: search_products(name={name}, category={category})")
    params = {}
    if name:
        params["name"] = name
    if category:
        params["category"] = category
    if min_price is not None:
        params["min_price"] = min_price
    if max_price is not None:
        params["max_price"] = max_price
    return _call_api("GET", "/api/v1/products/search", params=params)

def get_order_status(order_number: str) -> Union[Dict, str]:
    """根据订单号获取订单状态"""
    print(f"工具调用: get_order_status(order_number={order_number})")
    if not order_number:
        return "错误：订单号不能为空。"
    return _call_api("GET", f"/api/v1/orders/search", params={"order_number": order_number})

def get_active_promotions() -> Union[Dict, str]:
    """获取当前活跃的促销活动"""
    print("工具调用: get_active_promotions()")
    return _call_api("GET", "/api/v1/promotions/active")

def get_policy(policy_type: str) -> Union[Dict, str]:
    """获取政策信息（退换货、隐私政策等）"""
    print(f"工具调用: get_policy(policy_type={policy_type})")
    # 这里返回模拟的政策信息
    policies = {
        "return": "退换货政策：商品在收到后7天内可申请退换货，商品需保持原包装完好。",
        "privacy": "隐私政策：我们严格保护用户隐私，不会泄露个人信息给第三方。",
        "shipping": "配送政策：订单满99元免运费，一般3-5个工作日内送达。"
    }
    return policies.get(policy_type, "未找到相关政策信息")

def check_return_eligibility(order_id: int) -> Union[Dict, str]:
    """检查退换货资格"""
    print(f"工具调用: check_return_eligibility(order_id={order_id})")
    if not isinstance(order_id, int) or order_id <= 0:
        return "错误：订单ID必须是一个正整数。"
    return _call_api("GET", f"/api/v1/customer-service/refund-requests/{order_id}/eligibility")

def submit_return_request(order_id: int, reason: str, description: Optional[str] = None) -> Union[Dict, str]:
    """提交退换货申请"""
    print(f"工具调用: submit_return_request(order_id={order_id}, reason={reason})")
    if not isinstance(order_id, int) or order_id <= 0:
        return "错误：订单ID必须是一个正整数。"
    if not reason:
        return "错误：退换货原因不能为空。"

    data = {
        "order_id": order_id,
        "reason": reason,
        "description": description or ""
    }
    return _call_api("POST", "/api/v1/customer-service/refund-requests", json_data=data)

def cancel_order(order_id: int) -> Union[Dict, str]:
    """取消订单"""
    print(f"工具调用: cancel_order(order_id={order_id})")
    if not isinstance(order_id, int) or order_id <= 0:
        return "错误：订单ID必须是一个正整数。"
    return _call_api("POST", f"/api/v1/orders/{order_id}/cancel")

