"""
促销活动相关API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from decimal import Decimal

from app.core.database import get_db
from app.api.deps import get_current_active_user
from app.services.promotion import PromotionService
from app.schemas.customer_service import Promotion, Coupon
from app.models.customer_service import UserCoupon
from sqlalchemy import and_
from app.schemas.user import User

router = APIRouter()


@router.get("/active", response_model=List[Promotion])
def get_active_promotions(
    db: Session = Depends(get_db)
):
    """
    场景2: 售前咨询 - 活动与优惠
    获取当前有效的促销活动
    """
    promotion_service = PromotionService(db)
    promotions = promotion_service.get_active_promotions()
    
    return promotions


@router.get("/applicable")
def get_applicable_promotions(
    order_amount: float = Query(..., description="订单金额"),
    product_ids: Optional[str] = Query(None, description="商品ID列表，逗号分隔"),
    db: Session = Depends(get_db)
):
    """
    获取适用的促销活动
    根据订单金额和商品列表筛选可用的促销
    """
    promotion_service = PromotionService(db)
    
    # 解析商品ID列表
    product_id_list = []
    if product_ids:
        try:
            product_id_list = [int(pid.strip()) for pid in product_ids.split(",")]
        except ValueError:
            raise HTTPException(status_code=400, detail="商品ID格式错误")
    
    applicable_promotions = promotion_service.get_applicable_promotions(
        order_amount=Decimal(str(order_amount)),
        product_ids=product_id_list
    )
    
    return {
        "order_amount": order_amount,
        "applicable_promotions": applicable_promotions
    }


@router.get("/coupons/my-coupons", response_model=List[Coupon])
def get_my_coupons(
    is_used: Optional[bool] = Query(None, description="是否已使用"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取用户的优惠券列表
    """
    promotion_service = PromotionService(db)
    user_coupons = promotion_service.get_user_coupons(current_user.id, is_used=is_used)
    
    # 返回优惠券详情
    coupons = [uc.coupon for uc in user_coupons]
    return coupons


@router.get("/coupons/available", response_model=List[Coupon])
def get_available_coupons(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取用户可用的优惠券
    """
    promotion_service = PromotionService(db)
    coupons = promotion_service.get_available_coupons(current_user.id)
    
    return coupons


@router.post("/coupons/{code}/validate")
def validate_coupon(
    code: str,
    order_amount: float = Query(..., description="订单金额"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    验证优惠券是否可用
    检查优惠券的有效性和使用条件
    """
    promotion_service = PromotionService(db)
    
    validation_result = promotion_service.validate_coupon(
        code=code,
        user_id=current_user.id,
        order_amount=Decimal(str(order_amount))
    )
    
    return validation_result


@router.post("/coupons/{code}/use")
def use_coupon(
    code: str,
    order_id: int = Query(..., description="订单ID"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    使用优惠券
    """
    promotion_service = PromotionService(db)
    
    success = promotion_service.use_coupon(
        code=code,
        user_id=current_user.id,
        order_id=order_id
    )
    
    if not success:
        raise HTTPException(status_code=400, detail="优惠券使用失败")
    
    return {
        "message": "优惠券使用成功",
        "coupon_code": code,
        "order_id": order_id
    }


@router.get("/coupons/{code}")
def get_coupon_info(
    code: str,
    db: Session = Depends(get_db)
):
    """
    获取优惠券详细信息
    """
    promotion_service = PromotionService(db)
    coupon = promotion_service.get_coupon_by_code(code)
    
    if not coupon:
        raise HTTPException(status_code=404, detail="优惠券不存在")
    
    return {
        "code": coupon.code,
        "name": coupon.name,
        "discount_type": coupon.discount_type,
        "discount_value": float(coupon.discount_value),
        "min_order_amount": float(coupon.min_order_amount) if coupon.min_order_amount else None,
        "max_discount_amount": float(coupon.max_discount_amount) if coupon.max_discount_amount else None,
        "start_time": coupon.start_time,
        "end_time": coupon.end_time,
        "is_active": coupon.is_active,
        "usage_limit": coupon.usage_limit,
        "used_count": coupon.used_count
    }


@router.post("/coupons/{coupon_id}/claim")
def claim_coupon(
    coupon_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    领取优惠券
    """
    promotion_service = PromotionService(db)
    
    # 检查优惠券是否存在
    from app.models.customer_service import Coupon as CouponModel
    coupon = promotion_service.db.query(CouponModel).filter(CouponModel.id == coupon_id).first()
    if not coupon:
        raise HTTPException(status_code=404, detail="优惠券不存在")
    
    # 检查是否已经领取过
    existing_user_coupon = promotion_service.db.query(UserCoupon).filter(
        and_(
            UserCoupon.user_id == current_user.id,
            UserCoupon.coupon_id == coupon_id
        )
    ).first()
    
    if existing_user_coupon:
        raise HTTPException(status_code=400, detail="您已经领取过此优惠券")
    
    user_coupon = promotion_service.give_coupon_to_user(current_user.id, coupon_id)
    
    return {
        "message": "优惠券领取成功",
        "coupon_code": coupon.code,
        "obtained_at": user_coupon.obtained_at
    }


@router.get("/{promotion_id}", response_model=Promotion)
def get_promotion_detail(
    promotion_id: int,
    db: Session = Depends(get_db)
):
    """
    获取促销活动详细信息
    """
    promotion_service = PromotionService(db)
    promotion = promotion_service.get_promotion_by_id(promotion_id)
    
    if not promotion:
        raise HTTPException(status_code=404, detail="促销活动不存在")
    
    return promotion
