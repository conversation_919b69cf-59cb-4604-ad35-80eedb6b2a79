"""
简单的数据库连接测试脚本
"""

import pymysql

# 硬编码的数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '3425488440',
    'database': 'yue_ai_agent',
    'charset': 'utf8mb4'
}

def test_mysql_connection():
    """测试MySQL连接"""
    print("🔍 测试MySQL数据库连接...")
    print(f"📍 服务器: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"👤 用户: {DB_CONFIG['user']}")
    print(f"🗄️ 数据库: {DB_CONFIG['database']}")
    
    try:
        # 测试连接到MySQL服务器
        connection = pymysql.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            charset='utf8mb4'
        )
        
        print("✅ MySQL服务器连接成功!")
        
        # 检查数据库是否存在
        with connection.cursor() as cursor:
            cursor.execute("SHOW DATABASES LIKE %s", (DB_CONFIG['database'],))
            result = cursor.fetchone()
            
            if result:
                print(f"✅ 数据库 '{DB_CONFIG['database']}' 已存在")
            else:
                print(f"⚠️ 数据库 '{DB_CONFIG['database']}' 不存在，正在创建...")
                cursor.execute(f"CREATE DATABASE {DB_CONFIG['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                print(f"✅ 数据库 '{DB_CONFIG['database']}' 创建成功!")
        
        connection.close()
        
        # 测试连接到指定数据库
        connection = pymysql.connect(**DB_CONFIG)
        
        print(f"✅ 数据库 '{DB_CONFIG['database']}' 连接成功!")
        
        # 显示数据库信息
        with connection.cursor() as cursor:
            cursor.execute("SELECT DATABASE(), VERSION(), @@character_set_database, @@collation_database")
            db_info = cursor.fetchone()
            print(f"📊 当前数据库: {db_info[0]}")
            print(f"🔢 MySQL版本: {db_info[1]}")
            print(f"🔤 字符集: {db_info[2]}")
            print(f"📝 排序规则: {db_info[3]}")
        
        connection.close()
        return True
        
    except pymysql.Error as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

if __name__ == "__main__":
    print("🚀 YUE智能体平台 - 简单数据库连接测试")
    print("=" * 50)
    
    # 测试基础MySQL连接
    mysql_ok = test_mysql_connection()
    
    if mysql_ok:
        print("\n🎉 数据库连接测试通过!")
        print("\n📋 下一步:")
        print("1. 修复配置文件问题")
        print("2. 运行数据库迁移")
        print("3. 初始化测试数据")
        print("4. 启动服务")
    else:
        print("\n❌ 数据库连接测试失败，请检查:")
        print("1. MySQL服务是否启动")
        print("2. 用户名密码是否正确")
        print("3. 端口是否正确")
