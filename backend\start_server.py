"""
启动服务器脚本
"""

import uvicorn
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(__file__))

from app.core.config import settings

if __name__ == "__main__":
    print("🚀 启动YUE智能体综合应用平台后端服务...")
    print(f"📍 服务地址: http://{settings.HOST}:{settings.PORT}")
    print(f"📖 API文档: http://{settings.HOST}:{settings.PORT}/docs")
    print(f"🔧 调试模式: {'开启' if settings.DEBUG else '关闭'}")
    
    uvicorn.run(
        "app.main:socket_app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
