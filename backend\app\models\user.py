"""
用户相关数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text
from sqlalchemy.orm import relationship, foreign
from sqlalchemy.sql import func

from app.core.database import Base


class User(Base):
    """用户表"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    phone = Column(String(20))
    full_name = Column(String(100))
    hashed_password = Column(String(255))
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    is_vip = Column(Boolean, default=False)
    vip_level = Column(Integer, default=0)
    avatar_url = Column(String(255))
    address = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系
    orders = relationship("Order", back_populates="user", primaryjoin="User.id == foreign(Order.user_id)")
    complaints = relationship("Complaint", primaryjoin="User.id == foreign(Complaint.user_id)", back_populates="user")
    customer_service_sessions = relationship("CustomerServiceSession", back_populates="user", primaryjoin="User.id == foreign(CustomerServiceSession.user_id)")
    user_coupons = relationship("UserCoupon", back_populates="user", primaryjoin="User.id == foreign(UserCoupon.user_id)")
