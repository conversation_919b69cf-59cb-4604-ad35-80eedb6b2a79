# YUE智能体综合应用平台 - 快速启动指南

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装以下软件：

- **Python 3.11+**
- **Node.js 18+**
- **Docker & Docker Compose** (推荐)
- **Git**

### 2. 克隆项目

```bash
git clone <repository-url>
cd yue_ai_agent
```

### 3. 自动化安装 (推荐)

运行项目初始化脚本：

```bash
python scripts/setup.py
```

### 4. 手动安装

#### 4.1 后端环境设置

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装依赖
pip install -r backend/requirements.txt

# 复制环境配置
cp backend/.env.example backend/.env
```

#### 4.2 前端环境设置

```bash
cd frontend
npm install
cd ..
```

### 5. 配置环境变量

编辑 `backend/.env` 文件，配置以下关键参数：

```env
# 数据库配置
MYSQL_SERVER=localhost
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DB=yue_ai_agent

# AI模型配置
OPENAI_API_KEY=your_openai_api_key

# 其他服务配置
REDIS_HOST=localhost
MILVUS_HOST=localhost
MINIO_ENDPOINT=localhost:9000
```

### 6. 启动服务

#### 方式一：使用 Docker Compose (推荐)

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 方式二：手动启动

**启动基础服务：**

```bash
# 启动 MySQL
docker run -d --name mysql \
  -e MYSQL_ROOT_PASSWORD=password \
  -e MYSQL_DATABASE=yue_ai_agent \
  -p 3306:3306 mysql:8.0

# 启动 Redis
docker run -d --name redis -p 6379:6379 redis:7-alpine

# 启动 MinIO
docker run -d --name minio \
  -e MINIO_ROOT_USER=minioadmin \
  -e MINIO_ROOT_PASSWORD=minioadmin \
  -p 9000:9000 -p 9001:9001 \
  minio/minio server /data --console-address ":9001"

# 启动 Milvus (需要额外配置)
# 请参考 Milvus 官方文档
```

**启动应用服务：**

```bash
# 启动后端 (新终端)
cd backend
uvicorn app.main:socket_app --reload --host 0.0.0.0 --port 8000

# 启动前端 (新终端)
cd frontend
npm run dev
```

### 7. 访问应用

- **前端应用**: http://localhost:3000
- **后端API文档**: http://localhost:8000/docs
- **MinIO控制台**: http://localhost:9001

### 8. 初始化数据库

```bash
# 进入后端目录
cd backend

# 运行数据库迁移
alembic upgrade head

# 创建初始用户 (可选)
python -c "
from app.core.database import SessionLocal
from app.models.user import User
from app.core.security import get_password_hash

db = SessionLocal()
user = User(
    email='<EMAIL>',
    hashed_password=get_password_hash('admin123'),
    full_name='管理员',
    is_active=True,
    is_superuser=True
)
db.add(user)
db.commit()
print('管理员用户创建成功')
"
```

## 🔧 开发指南

### 项目结构

```
yue_ai_agent/
├── frontend/          # React前端
├── backend/           # FastAPI后端
├── docker/           # Docker配置
├── docs/             # 文档
└── scripts/          # 工具脚本
```

### 开发工作流

1. **前端开发**：
   ```bash
   cd frontend
   npm run dev
   ```

2. **后端开发**：
   ```bash
   cd backend
   uvicorn app.main:socket_app --reload
   ```

3. **数据库迁移**：
   ```bash
   cd backend
   alembic revision --autogenerate -m "描述"
   alembic upgrade head
   ```

### 常用命令

```bash
# 查看所有容器状态
docker-compose ps

# 重启特定服务
docker-compose restart backend

# 查看服务日志
docker-compose logs -f backend

# 进入容器
docker-compose exec backend bash

# 停止所有服务
docker-compose down

# 清理数据卷
docker-compose down -v
```

## 🐛 故障排除

### 常见问题

1. **端口冲突**：
   - 检查端口是否被占用：`netstat -an | grep :3000`
   - 修改 docker-compose.yml 中的端口映射

2. **数据库连接失败**：
   - 检查 MySQL 服务是否启动
   - 验证 .env 文件中的数据库配置

3. **前端依赖安装失败**：
   - 清除缓存：`npm cache clean --force`
   - 删除 node_modules 重新安装

4. **后端依赖安装失败**：
   - 升级 pip：`pip install --upgrade pip`
   - 使用国内镜像：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple`

### 获取帮助

- 查看详细文档：`docs/` 目录
- 提交 Issue：项目 GitHub 页面
- 联系开发团队：[联系方式]

## 🎯 下一步

1. 配置 AI 模型 API 密钥
2. 上传测试文档到知识库
3. 配置数据库连接用于 Text2SQL
4. 自定义文案模板
5. 部署到生产环境

祝您使用愉快！🎉
