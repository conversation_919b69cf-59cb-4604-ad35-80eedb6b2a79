# 应用配置
PROJECT_NAME=YUE智能体综合应用平台
VERSION=1.0.0
DEBUG=true
SECRET_KEY=your-secret-key-change-in-production

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置
MYSQL_SERVER=localhost
MYSQL_USER=root
MYSQL_PASSWORD=3425488440
MYSQL_DB=yue_ai_agent
MYSQL_PORT=3306

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Milvus配置
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_USER=
MILVUS_PASSWORD=

# MinIO配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=yue-ai-agent
MINIO_SECURE=false

# AI模型配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
DEFAULT_MODEL=gpt-3.5-turbo

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 文件上传配置
MAX_FILE_SIZE=104857600  # 100MB
UPLOAD_DIR=uploads

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
