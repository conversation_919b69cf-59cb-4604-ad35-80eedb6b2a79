"""
向数据库添加更多测试数据
"""

import sys
import os
from decimal import Decimal
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
# 获取当前脚本的目录 (backend/scripts)
current_dir = os.path.dirname(__file__)
# 获取backend目录
backend_dir = os.path.dirname(current_dir)
# 添加backend目录到Python路径，这样可以导入app模块
sys.path.insert(0, backend_dir)

from app.core.database import SessionLocal
from app.models.customer_service import *
from app.models.user import User
from app.core.security import get_password_hash


def add_more_data():
    """添加更多测试数据"""
    print("📊 向数据库添加更多测试数据...")
    
    db = SessionLocal()
    try:
        # 添加更多用户
        print("👥 添加更多用户...")
        new_users = [
            User(
                username="customer1",
                email="<EMAIL>",
                hashed_password=get_password_hash("customer123"),
                full_name="张三",
                is_active=True,
                is_vip=False,
                phone="13800138001",
                address="上海市浦东新区陆家嘴金融中心"
            ),
            User(
                username="customer2",
                email="<EMAIL>",
                hashed_password=get_password_hash("customer123"),
                full_name="李四",
                is_active=True,
                is_vip=True,
                vip_level=2,
                phone="13800138002",
                address="深圳市南山区科技园"
            ),
            User(
                username="customer3",
                email="<EMAIL>",
                hashed_password=get_password_hash("customer123"),
                full_name="王五",
                is_active=True,
                is_vip=True,
                vip_level=1,
                phone="13800138003",
                address="广州市天河区珠江新城"
            )
        ]
        
        for user in new_users:
            db.add(user)
        
        db.flush()
        
        # 添加更多产品分类
        print("📂 添加更多产品分类...")
        existing_categories = db.query(Category).all()
        
        new_categories = [
            Category(name="运动户外", description="运动装备和户外用品"),
            Category(name="美妆护肤", description="化妆品和护肤用品"),
            Category(name="食品饮料", description="各类食品和饮料"),
        ]
        
        for category in new_categories:
            db.add(category)
        
        db.flush()
        
        # 添加更多产品
        print("📦 添加更多产品...")
        all_categories = db.query(Category).all()
        
        new_products = [
            Product(
                name="MacBook Pro 14英寸",
                model="MKGR3CH/A",
                sku="MACBOOK14PRO512",
                description="Apple M2 Pro芯片，14英寸Liquid Retina XDR显示屏",
                specifications={"芯片": "M2 Pro", "内存": "16GB", "存储": "512GB SSD", "屏幕": "14英寸"},
                price=Decimal("15999.00"),
                original_price=Decimal("17999.00"),
                material="铝合金",
                size_guide="长度: 312.6mm, 宽度: 221.2mm, 厚度: 15.5mm",
                images=["macbook_1.jpg", "macbook_2.jpg"],
                category_id=all_categories[0].id,
                is_featured=True
            ),
            Product(
                name="Adidas Ultra Boost 22",
                model="GX5915",
                sku="ADIDAS_UB22_BLACK_42",
                description="经典跑鞋，Boost中底科技",
                specifications={"颜色": "黑色", "尺码": "42", "科技": "Boost中底"},
                price=Decimal("1299.00"),
                original_price=Decimal("1599.00"),
                material="Primeknit针织鞋面",
                size_guide="建议选择正常尺码或大半码",
                images=["adidas_ub22_1.jpg", "adidas_ub22_2.jpg"],
                category_id=all_categories[1].id if len(all_categories) > 1 else all_categories[0].id,
                is_featured=True
            ),
            Product(
                name="SK-II神仙水",
                model="SKII230ML",
                sku="SKII_PITERA_230ML",
                description="含PITERA™精华，改善肌肤质感",
                specifications={"容量": "230ml", "功效": "保湿修护", "适用": "所有肌肤"},
                price=Decimal("1690.00"),
                original_price=Decimal("1890.00"),
                material="PITERA™酵母精华",
                size_guide="建议早晚使用，取适量轻拍至吸收",
                images=["skii_1.jpg", "skii_2.jpg"],
                category_id=new_categories[1].id,
                is_featured=False
            ),
            Product(
                name="小米13 Pro",
                model="2210132C",
                sku="MI13PRO_256GB_WHITE",
                description="骁龙8 Gen 2处理器，徕卡影像",
                specifications={"处理器": "骁龙8 Gen 2", "内存": "12GB", "存储": "256GB", "屏幕": "6.73英寸"},
                price=Decimal("4999.00"),
                original_price=Decimal("5499.00"),
                material="陶瓷机身",
                size_guide="长度: 162.9mm, 宽度: 74.6mm, 厚度: 8.38mm",
                images=["mi13pro_1.jpg", "mi13pro_2.jpg"],
                category_id=all_categories[0].id,
                is_featured=True
            )
        ]
        
        for product in new_products:
            db.add(product)
        
        db.flush()
        
        # 为新产品创建库存
        print("📊 创建库存记录...")
        for product in new_products:
            inventory = Inventory(
                product_id=product.id,
                stock_quantity=50,
                available_quantity=50,
                low_stock_threshold=5
            )
            db.add(inventory)
        
        # 添加更多订单
        print("🛒 添加更多订单...")
        new_orders = [
            Order(
                order_number="ORD20241202001",
                user_id=new_users[0].id,
                status=OrderStatus.SHIPPED,
                total_amount=Decimal("15999.00"),
                final_amount=Decimal("15999.00"),
                shipping_address={
                    "name": "张三",
                    "phone": "13800138001",
                    "address": "上海市浦东新区陆家嘴金融中心1号楼"
                },
                shipping_method="京东快递",
                payment_method="支付宝"
            ),
            Order(
                order_number="ORD20241202002",
                user_id=new_users[1].id,
                status=OrderStatus.PENDING_PAYMENT,
                total_amount=Decimal("2989.00"),
                final_amount=Decimal("2989.00"),
                shipping_address={
                    "name": "李四",
                    "phone": "13800138002",
                    "address": "深圳市南山区科技园北区"
                },
                shipping_method="顺丰快递",
                payment_method="微信支付"
            ),
            Order(
                order_number="ORD20241202003",
                user_id=new_users[2].id,
                status=OrderStatus.DELIVERED,
                total_amount=Decimal("1690.00"),
                discount_amount=Decimal("100.00"),
                final_amount=Decimal("1590.00"),
                shipping_address={
                    "name": "王五",
                    "phone": "13800138003",
                    "address": "广州市天河区珠江新城CBD"
                },
                shipping_method="圆通快递",
                payment_method="银行卡"
            )
        ]
        
        for order in new_orders:
            db.add(order)
        
        db.flush()
        
        # 为订单添加商品
        print("📝 添加订单商品...")
        order_items = [
            # 第一个订单：MacBook
            OrderItem(
                order_id=new_orders[0].id,
                product_id=new_products[0].id,
                quantity=1,
                unit_price=Decimal("15999.00"),
                total_price=Decimal("15999.00"),
                specifications={"芯片": "M2 Pro", "内存": "16GB", "存储": "512GB"}
            ),
            # 第二个订单：Adidas鞋 + 小米手机
            OrderItem(
                order_id=new_orders[1].id,
                product_id=new_products[1].id,
                quantity=1,
                unit_price=Decimal("1299.00"),
                total_price=Decimal("1299.00"),
                specifications={"颜色": "黑色", "尺码": "42"}
            ),
            OrderItem(
                order_id=new_orders[1].id,
                product_id=new_products[3].id,
                quantity=1,
                unit_price=Decimal("1690.00"),
                total_price=Decimal("1690.00"),
                specifications={"颜色": "白色", "存储": "256GB"}
            ),
            # 第三个订单：SK-II
            OrderItem(
                order_id=new_orders[2].id,
                product_id=new_products[2].id,
                quantity=1,
                unit_price=Decimal("1690.00"),
                total_price=Decimal("1690.00"),
                specifications={"容量": "230ml"}
            )
        ]
        
        for item in order_items:
            db.add(item)
        
        # 添加物流信息
        print("🚚 添加物流信息...")
        logistics_records = [
            LogisticsTracking(
                order_id=new_orders[0].id,
                tracking_number="JD1234567890",
                logistics_company="京东快递",
                logistics_company_code="JD",
                current_status="运输中",
                tracking_info=[
                    {"time": "2024-12-02 10:00:00", "status": "已下单", "location": "上海"},
                    {"time": "2024-12-02 14:00:00", "status": "已发货", "location": "上海分拣中心"},
                    {"time": "2024-12-03 09:00:00", "status": "运输中", "location": "上海-浦东区"}
                ]
            ),
            LogisticsTracking(
                order_id=new_orders[2].id,
                tracking_number="YT9876543210",
                logistics_company="圆通快递",
                logistics_company_code="YTO",
                current_status="已签收",
                actual_delivery=datetime.now() - timedelta(days=1),
                tracking_info=[
                    {"time": "2024-12-01 11:00:00", "status": "已下单", "location": "广州"},
                    {"time": "2024-12-01 15:00:00", "status": "已发货", "location": "广州分拣中心"},
                    {"time": "2024-12-02 10:00:00", "status": "派送中", "location": "天河区"},
                    {"time": "2024-12-02 16:30:00", "status": "已签收", "location": "珠江新城"}
                ]
            )
        ]
        
        for logistics in logistics_records:
            db.add(logistics)
        
        # 添加更多优惠券
        print("🎫 添加更多优惠券...")
        existing_promotion = db.query(Promotion).first()
        
        new_coupons = [
            Coupon(
                code="VIP20",
                name="VIP专享20%折扣券",
                promotion_id=existing_promotion.id,
                discount_type="percentage",
                discount_value=Decimal("20"),
                min_order_amount=Decimal("1000"),
                max_discount_amount=Decimal("200"),
                usage_limit=100,
                start_time=datetime.now(),
                end_time=datetime.now() + timedelta(days=30)
            ),
            Coupon(
                code="NEWUSER100",
                name="新用户100元券",
                promotion_id=existing_promotion.id,
                discount_type="fixed_amount",
                discount_value=Decimal("100"),
                min_order_amount=Decimal("500"),
                usage_limit=1000,
                start_time=datetime.now(),
                end_time=datetime.now() + timedelta(days=60)
            )
        ]
        
        for coupon in new_coupons:
            db.add(coupon)
        
        db.flush()
        
        # 给用户发放优惠券
        for user in new_users:
            for coupon in new_coupons:
                user_coupon = UserCoupon(
                    user_id=user.id,
                    coupon_id=coupon.id
                )
                db.add(user_coupon)
        
        # 添加退换货申请
        print("📋 添加退换货申请...")
        refund_requests = [
            RefundRequest(
                request_number="REF20241202001",
                order_id=new_orders[2].id,
                user_id=new_users[2].id,
                type="refund",
                reason="商品与描述不符",
                description="收到的商品包装有破损，影响使用",
                status=RefundStatus.PENDING,
                refund_amount=Decimal("1590.00"),
                evidence_images=["evidence1.jpg", "evidence2.jpg"]
            )
        ]
        
        for request in refund_requests:
            db.add(request)
        
        # 添加投诉建议
        print("💬 添加投诉建议...")
        complaints = [
            Complaint(
                complaint_number="CMP20241202001",
                user_id=new_users[0].id,
                type="complaint",
                category="logistics",
                title="物流速度太慢",
                content="订单已经3天了还在运输中，希望能加快配送速度",
                status=ComplaintStatus.OPEN,
                priority="normal"
            ),
            Complaint(
                complaint_number="CMP20241202002",
                user_id=new_users[1].id,
                type="suggestion",
                category="product",
                title="建议增加更多颜色选择",
                content="希望能增加更多手机颜色选择，特别是粉色系列",
                status=ComplaintStatus.IN_PROGRESS,
                priority="low"
            )
        ]
        
        for complaint in complaints:
            db.add(complaint)
        
        # 添加客服会话
        print("💬 添加客服会话...")
        sessions = [
            CustomerServiceSession(
                session_id="session_001",
                user_id=new_users[0].id,
                agent_type="ai",
                agent_id="ai_agent_001",
                status="closed",
                start_time=datetime.now() - timedelta(hours=2),
                end_time=datetime.now() - timedelta(hours=1),
                satisfaction_rating=4,
                summary="用户咨询订单状态，已解答"
            ),
            CustomerServiceSession(
                session_id="session_002",
                user_id=new_users[1].id,
                agent_type="human",
                agent_id="human_agent_001",
                status="active",
                start_time=datetime.now() - timedelta(minutes=30)
            )
        ]
        
        for session in sessions:
            db.add(session)
        
        db.flush()
        
        # 添加客服消息
        messages = [
            CustomerServiceMessage(
                session_id="session_001",
                sender_type="user",
                sender_id=str(new_users[0].id),
                content="你好，我想查询一下我的订单状态"
            ),
            CustomerServiceMessage(
                session_id="session_001",
                sender_type="agent",
                sender_id="ai_agent_001",
                content="您好！我来帮您查询订单状态。您的订单ORD20241202001目前状态是运输中，预计明天送达。"
            ),
            CustomerServiceMessage(
                session_id="session_002",
                sender_type="user",
                sender_id=str(new_users[1].id),
                content="我要申请退款"
            ),
            CustomerServiceMessage(
                session_id="session_002",
                sender_type="agent",
                sender_id="human_agent_001",
                content="好的，我来为您处理退款申请。请问是哪个订单需要退款？"
            )
        ]
        
        for message in messages:
            db.add(message)
        
        db.commit()
        print("✅ 数据添加完成！")
        
        # 显示统计信息
        print("\n📊 数据库统计信息:")
        print(f"   用户总数: {db.query(User).count()}")
        print(f"   产品总数: {db.query(Product).count()}")
        print(f"   订单总数: {db.query(Order).count()}")
        print(f"   优惠券总数: {db.query(Coupon).count()}")
        print(f"   退换货申请: {db.query(RefundRequest).count()}")
        print(f"   投诉建议: {db.query(Complaint).count()}")
        print(f"   客服会话: {db.query(CustomerServiceSession).count()}")
        print(f"   客服消息: {db.query(CustomerServiceMessage).count()}")
        
    except Exception as e:
        print(f"❌ 数据添加失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    add_more_data()
