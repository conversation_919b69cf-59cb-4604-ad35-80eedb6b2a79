#!/usr/bin/env python3
"""
简化的大模型配置测试脚本
专门测试DeepSeek模型配置和基础流式输出功能
"""

import asyncio
import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_model_config():
    """测试模型配置"""
    print("🔧 测试DeepSeek模型配置")
    print("-" * 50)
    
    try:
        from app.core.config import settings
        
        print(f"✅ 环境变量配置:")
        print(f"   - API Key: {settings.OPENAI_API_KEY[:10] if settings.OPENAI_API_KEY else 'None'}...{settings.OPENAI_API_KEY[-4:] if settings.OPENAI_API_KEY else ''}")
        print(f"   - API Base: {settings.OPENAI_API_BASE}")
        print(f"   - Model: {settings.LLM_MODEL}")
        
        if not settings.OPENAI_API_KEY:
            print("❌ API Key未配置")
            return False
            
        if not settings.OPENAI_API_BASE:
            print("❌ API Base未配置")
            return False
            
        print("✅ 基础配置检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

async def test_model_client():
    """测试模型客户端初始化"""
    print("\n🤖 测试模型客户端初始化")
    print("-" * 50)
    
    try:
        from app.core.llms import model_client
        
        print(f"✅ 模型客户端类型: {type(model_client).__name__}")
        print("✅ 模型客户端初始化成功")
        return True
        
    except Exception as e:
        print(f"❌ 模型客户端初始化失败: {e}")
        logger.exception("详细错误信息:")
        return False

async def test_direct_api_call():
    """直接测试API调用"""
    print("\n📡 测试直接API调用")
    print("-" * 50)
    
    try:
        import httpx
        from app.core.config import settings
        
        headers = {
            "Authorization": f"Bearer {settings.OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": settings.LLM_MODEL,
            "messages": [
                {"role": "user", "content": "你好，请简单回复一下"}
            ],
            "stream": False,
            "max_tokens": 50
        }
        
        print("📤 发送API请求...")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{settings.OPENAI_API_BASE}/chat/completions",
                headers=headers,
                json=data
            )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
            print(f"✅ API调用成功")
            print(f"📥 响应内容: {content}")
            return True
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API调用异常: {e}")
        logger.exception("详细错误信息:")
        return False

async def test_streaming_api_call():
    """测试流式API调用"""
    print("\n🌊 测试流式API调用")
    print("-" * 50)
    
    try:
        import httpx
        from app.core.config import settings
        
        headers = {
            "Authorization": f"Bearer {settings.OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": settings.LLM_MODEL,
            "messages": [
                {"role": "user", "content": "请用一句话介绍你自己"}
            ],
            "stream": True,
            "max_tokens": 100
        }
        
        print("📤 发送流式API请求...")
        print("📥 流式响应:")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            async with client.stream(
                "POST",
                f"{settings.OPENAI_API_BASE}/chat/completions",
                headers=headers,
                json=data
            ) as response:
                
                if response.status_code != 200:
                    print(f"❌ 流式API调用失败: {response.status_code}")
                    return False
                
                chunk_count = 0
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        chunk_data = line[6:]  # 移除 "data: " 前缀
                        if chunk_data.strip() == "[DONE]":
                            break
                        
                        try:
                            import json
                            chunk_json = json.loads(chunk_data)
                            delta = chunk_json.get("choices", [{}])[0].get("delta", {})
                            content = delta.get("content", "")
                            if content:
                                print(content, end="", flush=True)
                                chunk_count += 1
                        except json.JSONDecodeError:
                            continue
                
                print(f"\n✅ 流式API调用成功")
                print(f"   收到 {chunk_count} 个数据块")
                return True
                
    except Exception as e:
        print(f"❌ 流式API调用异常: {e}")
        logger.exception("详细错误信息:")
        return False

async def test_autogen_client():
    """测试AutoGen客户端"""
    print("\n🔄 测试AutoGen客户端")
    print("-" * 50)

    try:
        from app.core.llms import model_client
        from autogen_core.models import UserMessage

        print("📤 发送测试消息...")

        # 创建测试消息 - 使用正确的消息类型
        messages = [UserMessage(content="你好，请简单介绍一下你自己", source="user")]

        # 调用模型
        response = await model_client.create(messages)

        if response and response.content:
            print(f"✅ AutoGen客户端调用成功")
            print(f"📥 响应内容: {response.content}")
            return True
        else:
            print("❌ AutoGen客户端未返回有效响应")
            return False

    except Exception as e:
        print(f"❌ AutoGen客户端调用失败: {e}")
        logger.exception("详细错误信息:")
        return False

async def main():
    """主测试函数"""
    print("🚀 DeepSeek大模型简化测试")
    print("=" * 60)
    
    # 测试结果统计
    test_results = []
    
    # 1. 测试模型配置
    result1 = await test_model_config()
    test_results.append(("模型配置检查", result1))
    
    if not result1:
        print("\n❌ 基础配置失败，无法继续测试")
        return
    
    # 2. 测试模型客户端初始化
    result2 = await test_model_client()
    test_results.append(("模型客户端初始化", result2))
    
    # 3. 测试直接API调用
    result3 = await test_direct_api_call()
    test_results.append(("直接API调用", result3))
    
    # 4. 测试流式API调用
    result4 = await test_streaming_api_call()
    test_results.append(("流式API调用", result4))
    
    # 5. 测试AutoGen客户端（如果前面的测试通过）
    if result2:
        result5 = await test_autogen_client()
        test_results.append(("AutoGen客户端", result5))
    
    # 输出测试总结
    print("\n📊 测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！DeepSeek大模型配置成功！")
    elif passed >= total * 0.8:
        print("⚠️ 大部分测试通过，基本配置正确")
    else:
        print("❌ 多项测试失败，需要检查配置")

if __name__ == "__main__":
    # 确保在正确的目录中运行
    os.chdir(Path(__file__).parent)
    
    # 运行测试
    asyncio.run(main())
